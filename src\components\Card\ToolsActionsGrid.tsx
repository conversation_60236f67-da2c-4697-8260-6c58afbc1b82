import {
  IconEdit,
  IconRefresh,
  IconEye,
  IconPhoto,
  IconTrash,
  IconCoin,
  IconRepeat,
} from '@tabler/icons-react';
import {
  Anchor,
  Card,
  Group,
  SimpleGrid,
  Text,
  UnstyledButton,
  useMantineTheme,
} from '@mantine/core';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { notifications } from '@mantine/notifications';
import { modals } from '@mantine/modals';
import { fetchData, deleteData, deleteFile } from '../../lib/supabase';
import { MAX_CONTACTS_LIMIT } from '../../lib/config';
import { NameModal } from '../Modals/name';
import { TransferModal } from '../Modals/transfer';
import { ImageUploadModal } from '../Modals/imageUpload';
import { MintNftModal } from '../Modals/mintNft';
import { getFirstImage } from '../../lib/common';
import classes from './ActionsGrid.module.css';

const mockdata = [
  { title: 'Update Record', icon: IconEdit, color: 'blue', action: 'update-record' },
  { title: 'Update Name', icon: IconRefresh, color: 'indigo', action: 'update-name' },
  { title: 'Update Image', icon: IconPhoto, color: 'green', action: 'update-image' },
  { title: 'Apply NFT', icon: IconCoin, color: 'orange', action: 'apply-nft' },
  { title: 'Delete Name', icon: IconTrash, color: 'red', action: 'delete-name' },
  { title: 'Transfer', icon: IconRepeat, color: 'teal', action: 'transfer' },
  { title: 'View Profile Page', icon: IconEye, color: 'violet', action: 'view-profile', wide: true },
];

interface ActionsGridProps {
  onContactCreated?: () => void; // Optional callback to refresh parent contacts
  refreshTrigger?: number; // Trigger to refresh local contacts when parent changes
  contactName?: string; // Specific contact name to work with
}

export function ToolsActionsGrid({ onContactCreated, refreshTrigger, contactName }: ActionsGridProps) {
  const theme = useMantineTheme();
  const router = useRouter();
  const { data: session } = useSession();
  const [contacts, setContacts] = useState<any[]>([]);
  const [currentContact, setCurrentContact] = useState<any>(null);
  const [nameModalOpened, setNameModalOpened] = useState(false);
  const [transferModalOpened, setTransferModalOpened] = useState(false);
  const [imageModalOpened, setImageModalOpened] = useState(false);
  const [mintNftModalOpened, setMintNftModalOpened] = useState(false);
  const [existingNameToEdit, setExistingNameToEdit] = useState<string | undefined>(undefined);
  const [selectedContactName, setSelectedContactName] = useState<string>('');

  const fetchContacts = async () => {
    if (session?.user?.email) {
      const { data, error } = await fetchData('contact', {
        select: 'name, image, profile, profile_email, email, images, description, social, crypto, web2, web3',
        filter: [{
          column: 'profile_email',
          value: session.user.email
        }]
      });

      if (!error && data) {
        setContacts(data as any[]);
      }
    }
  };

  const fetchCurrentContact = async () => {
    if (contactName) {
      const { data, error } = await fetchData('contact', {
        select: 'name, image, profile, profile_email, email, images, description, social, crypto, web2, web3',
        filter: [{
          column: 'name',
          value: contactName.toLowerCase()
        }],
        single: true
      });

      if (!error && data) {
        const contactData = Array.isArray(data) ? data[0] : data;
        // Always set the contact data for viewing purposes
        setCurrentContact(contactData);
      }
    }
  };

  useEffect(() => {
    fetchContacts();
    fetchCurrentContact();
  }, [session, contactName]);

  // Refresh local contacts when refreshTrigger changes (when contacts are deleted/updated)
  useEffect(() => {
    if (refreshTrigger !== undefined && refreshTrigger > 0) {
      fetchContacts();
      fetchCurrentContact();
    }
  }, [refreshTrigger]);

  const handleContactSaved = (newName?: string) => {
    // If a new name was provided (name update), redirect to the new URL
    if (newName && newName !== contactName) {
      // Show success notification
      notifications.show({
        title: 'Success',
        message: 'Contact name updated successfully',
        color: 'green',
      });

      // Redirect to the new URL with the updated name
      router.push(`/tools/${newName.toLowerCase()}`);
      return;
    }

    // Refresh local contacts state
    fetchContacts();
    fetchCurrentContact();
    // Also refresh parent contacts if callback provided
    if (onContactCreated) {
      onContactCreated();
    }
  };

  const handleTransferCompleted = () => {
    // Refresh local contacts state after transfer
    fetchContacts();
    fetchCurrentContact();
    // Also refresh parent contacts if callback provided
    if (onContactCreated) {
      onContactCreated();
    }
  };

  const openNewContactModal = () => {
    // Check if limit is reached
    if (contacts.length >= MAX_CONTACTS_LIMIT) {
      notifications.show({
        title: 'Creation Limit Reached',
        message: `You can only create a maximum of ${MAX_CONTACTS_LIMIT} ODude Names. Please delete an existing name to create a new one.`,
        color: 'orange',
      });
      return;
    }

    setExistingNameToEdit(undefined);
    setNameModalOpened(true);
  };

  const openTransferModal = (contactNameParam?: string) => {
    // If a specific contact is provided, use it directly
    if (contactNameParam) {
      setTransferModalOpened(true);
      return;
    }

    // Check if user has any contacts to transfer (fallback behavior)
    if (contacts.length === 0) {
      notifications.show({
        title: 'No Contacts Found',
        message: 'You need to have at least one ODude Name to transfer.',
        color: 'orange',
      });
      return;
    }
    setTransferModalOpened(true);
  };

  const openImageUploadModal = (contactNameParam: string) => {
    setSelectedContactName(contactNameParam);
    setImageModalOpened(true);
  };

  const openMintNftModal = (contactNameParam: string) => {
    setSelectedContactName(contactNameParam);
    setMintNftModalOpened(true);
  };

  const openUpdateContactModal = (contactNameParam: string) => {
    setExistingNameToEdit(contactNameParam);
    setNameModalOpened(true);
  };

  // Check if NFT is already minted (has image value)
  const isNftMinted = (contact: any) => {
    return !!(contact?.image && contact.image.trim() !== '');
  };

  const handleDeleteName = async (contactName: string) => {
    modals.openConfirmModal({
      title: 'Delete Contact',
      centered: true,
      children: (
        <Text size="sm">
          Are you sure you want to delete "{contactName}"? This action will permanently remove:
          <br />• The contact record
          <br />• All bookmarks for this contact
          <br />• All uploaded images
          <br /><br />
          This action cannot be undone.
        </Text>
      ),
      labels: { confirm: 'Delete', cancel: 'Cancel' },
      confirmProps: { color: 'red' },
      onCancel: () => console.log('Delete cancelled'),
      onConfirm: async () => {
        try {
          // Show loading notification
          const loadingNotification = notifications.show({
            title: 'Deleting...',
            message: 'Please wait while we delete the contact',
            loading: true,
            autoClose: false,
          });

          // Find the contact to get image data
          const contact = contacts.find(c => c.name.toLowerCase() === contactName.toLowerCase());

          // Delete all images first
          if (contact?.images) {
            const images = contact.images;
            for (const [key, imageUrl] of Object.entries(images)) {
              if (imageUrl) {
                try {
                  // Extract file path from URL
                  const urlParts = (imageUrl as string).split('/');
                  const fileName = urlParts[urlParts.length - 1];
                  const filePath = `profile-images/${fileName}`;
                  await deleteFile('images', filePath);
                } catch (error) {
                  console.error(`Error deleting image ${key}:`, error);
                }
              }
            }
          }

          // Delete all bookmarks for this contact
          const bookmarks = await fetchData('bookmark', {
            filter: [{ column: 'contact_name', value: contactName.toLowerCase() }]
          });

          if (bookmarks.data && Array.isArray(bookmarks.data)) {
            for (const bookmark of bookmarks.data) {
              await deleteData('bookmark', { column: 'id', value: (bookmark as any).id });
            }
          }

          // Delete the contact record
          await deleteData('contact', { column: 'name', value: contactName.toLowerCase() });

          // Hide loading notification
          notifications.hide(loadingNotification);

          // Show success notification
          notifications.show({
            title: 'Success',
            message: 'Contact deleted successfully',
            color: 'green',
          });

          // Refresh contacts
          fetchContacts();
          fetchCurrentContact();
          if (onContactCreated) {
            onContactCreated();
          }

          // Redirect to front page after successful deletion
          router.push('/');

        } catch (error) {
          console.error('Error deleting contact:', error);
          notifications.show({
            title: 'Error',
            message: 'Failed to delete contact. Please try again.',
            color: 'red',
          });
        }
      },
    });
  };

  const handleItemClick = (item: typeof mockdata[0]) => {
    // If we have a specific contact (contactName prop), work with that contact directly
    if (contactName) {
      // Use currentContact if available, otherwise create a minimal contact object
      const targetContact = currentContact || { name: contactName };
      handleActionForContact(item.action, targetContact);
      return;
    }

    // Handle Transfer action (doesn't require specific contact)
    if (item.title === 'Transfer') {
      openTransferModal();
      return;
    }

    // Fallback to old behavior for backward compatibility when no specific contactName is provided
    if (['Update Record', 'Update Name', 'Update Image', 'View Profile Page', 'Apply NFT', 'Delete Name'].includes(item.title)) {
      if (contacts.length === 0) {
        notifications.show({
          title: 'No Contacts Found',
          message: 'You need to have at least one ODude Name to perform this action.',
          color: 'orange',
        });
        return;
      }

      // If user has only one contact, use it directly
      if (contacts.length === 1) {
        const contact = contacts[0];
        handleActionForContact(item.action, contact);
      } else {
        // If user has multiple contacts, show a selection modal
        showContactSelectionModal(item.action, item.title);
      }
    }
  };

  const handleActionForContact = (action: string, contact: any) => {
    // Check if user owns this contact for actions that require ownership
    const userOwnsContact = session?.user?.email && contact.profile_email === session.user.email;
    const ownershipRequiredActions = ['update-record', 'update-name', 'update-image', 'apply-nft', 'delete-name'];

    if (ownershipRequiredActions.includes(action) && !userOwnsContact) {
      notifications.show({
        title: 'Access Denied',
        message: 'You can only modify contacts that you own.',
        color: 'red',
      });
      return;
    }

    switch (action) {
      case 'update-record':
        router.push(`/update?contact_name=${contact.name.toLowerCase()}`);
        break;
      case 'update-name':
        if (!isNftMinted(contact)) {
          openUpdateContactModal(contact.name);
        } else {
          notifications.show({
            title: 'Cannot Update Name',
            message: 'This contact has an NFT applied and cannot be renamed.',
            color: 'orange',
          });
        }
        break;
      case 'update-image':
        openImageUploadModal(contact.name);
        break;
      case 'view-profile':
        router.push(`/profile/${contact.name.toLowerCase()}`);
        break;
      case 'apply-nft':
        if (!isNftMinted(contact)) {
          openMintNftModal(contact.name);
        } else {
          notifications.show({
            title: 'NFT Already Applied',
            message: 'This contact already has an NFT applied.',
            color: 'orange',
          });
        }
        break;
      case 'delete-name':
        handleDeleteName(contact.name);
        break;
      case 'transfer':
        openTransferModal(contact.name);
        break;
    }
  };

  const showContactSelectionModal = (action: string, actionTitle: string) => {
    modals.open({
      title: `Select Contact for ${actionTitle}`,
      centered: true,
      children: (
        <div>
          <Text size="sm" mb="md">
            Select which contact you want to {actionTitle.toLowerCase()}:
          </Text>
          {contacts.map((contact) => (
            <UnstyledButton
              key={contact.name}
              onClick={() => {
                modals.closeAll();
                handleActionForContact(action, contact);
              }}
              style={{
                display: 'block',
                width: '100%',
                padding: '8px',
                marginBottom: '4px',
                borderRadius: '4px',
                border: '1px solid #e0e0e0',
                textAlign: 'left',
              }}
            >
              <Text size="sm" fw={500}>{contact.profile || contact.name}</Text>
              <Text size="xs" c="dimmed">{contact.name}</Text>
            </UnstyledButton>
          ))}
        </div>
      ),
    });
  };

  const regularItems = mockdata.filter(item => !item.wide).map((item) => (
    <UnstyledButton
      key={item.title}
      className={classes.item}
      onClick={() => handleItemClick(item)}
      style={{ cursor: 'pointer' }}
    >
      <item.icon color={theme.colors[item.color][6]} size={24} />
      <Text size="xs" mt={7}>
        {item.title}
      </Text>
    </UnstyledButton>
  ));

  const wideItems = mockdata.filter(item => item.wide).map((item) => (
    <UnstyledButton
      key={item.title}
      className={`${classes.item} ${classes.wideItem}`}
      onClick={() => handleItemClick(item)}
      style={{ cursor: 'pointer' }}
    >
      <item.icon color={theme.colors[item.color][6]} size={40} />
      <Text size="xs" mt={2}>
        {item.title}
      </Text>
    </UnstyledButton>
  ));

  return (
    <>
      <NameModal
        opened={nameModalOpened}
        onClose={() => setNameModalOpened(false)}
        existingName={existingNameToEdit}
        onContactSaved={handleContactSaved}
      />
      <TransferModal
        opened={transferModalOpened}
        onClose={() => setTransferModalOpened(false)}
        contacts={contacts}
        currentUserEmail={session?.user?.email || ''}
        onTransferCompleted={handleTransferCompleted}
        contactName={contactName}
      />
      <ImageUploadModal
        opened={imageModalOpened}
        onClose={() => setImageModalOpened(false)}
        contactName={contactName || selectedContactName}
        onContactUpdated={handleContactSaved}
      />
      <MintNftModal
        opened={mintNftModalOpened}
        onClose={() => setMintNftModalOpened(false)}
        contactName={contactName || selectedContactName}
        userInfo={currentContact || contacts.find(c => c.name === selectedContactName)}
        onNftMinted={handleContactSaved}
      />
      <Card withBorder radius="md" className={classes.card}>
        <Group justify="space-between">
          <Text className={classes.title}>Tools</Text>
          <Text size="xs" c="dimmed" style={{ lineHeight: 1 }}>
            ODude
          </Text>
        </Group>
        <SimpleGrid cols={2} mt="md">
          {regularItems}
        </SimpleGrid>
        {wideItems.length > 0 && (
          <div style={{ marginTop: '12px' }}>
            {wideItems}
          </div>
        )}
      </Card>
    </>
  );
}