'use client';

import { Flex, Anchor, Text } from "@mantine/core";
import { useFooter } from "src/providers/FooterProvider";
import Link from "next/link";

export const Footer = () => {
  const { footerContent } = useFooter();

  // Always render footer with links, but show dynamic content if available
  return (
    <Flex
      mih={50}
      bg="rgba(0, 0, 0, .3)"
      gap="md"
      justify="center"
      align="center"
      direction="row"
      wrap="wrap"
      style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 100
      }}
    >
      {footerContent ? (
        <>
          {footerContent}
          <Text c="dimmed" size="sm">|</Text>
        </>
      ) : null}

      <Anchor component={Link} href="/privacy-policy" size="sm" c="dimmed">
        Privacy Policy
      </Anchor>
      <Text c="dimmed" size="sm">|</Text>
      <Anchor component={Link} href="/contact-us" size="sm" c="dimmed">
        Contact Us
      </Anchor>
    </Flex>
  );
};
